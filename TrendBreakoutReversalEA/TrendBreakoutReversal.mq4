//+------------------------------------------------------------------+
//|                                        TrendBreakoutReversal.mq4 |
//|                                    趋势突破与反转确认做多策略 XAUUSD |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "TrendBreakoutReversal EA"
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 输入参数                                                          |
//+------------------------------------------------------------------+
// 指标参数
input int    EMA20_Period = 20;           // EMA20周期
input int    EMA50_Period = 50;           // EMA50周期
input int    RSI_Period = 14;             // RSI周期
input int    MACD_Fast = 12;              // MACD快线
input int    MACD_Slow = 26;              // MACD慢线
input int    MACD_Signal = 9;             // MACD信号线
input int    Volume_MA_Period = 20;       // 成交量均线周期
input int    ATR_Period = 14;             // ATR周期
input int    Breakout_Lookback = 20;      // 突破回看周期

// 风险管理参数
input double Risk_Percent = 2.0;          // 风险比例(%)
input double Profit_Ratio = 2.0;          // 盈亏比
input double Max_Spread = 3.0;            // 最大点差(点)
input bool   Use_Fixed_TP = true;         // 使用固定止盈
input bool   Use_Trailing_Stop = false;   // 使用追踪止损

// 追踪止损参数
input double Trail_Trigger_Points = 20.0; // 追踪触发距离(点)
input double Trail_Stop_Points = 10.0;    // 追踪止损距离(点)
input double Trail_Step_Points = 5.0;     // 追踪步进(点)

// 时间过滤参数
input bool   Use_Time_Filter = true;      // 启用时间过滤
input int    Start_Hour = 8;              // 开始小时(服务器时间)
input int    End_Hour = 22;               // 结束小时(服务器时间)

// 信号过滤参数
input double RSI_Oversold = 30.0;         // RSI超卖阈值
input double ATR_Support_Factor = 1.0;    // ATR支撑因子
input double Volume_Factor = 1.2;         // 成交量因子

// 面板显示参数
input bool   Show_Dashboard = true;       // 显示仪表板
input int    Dashboard_X = 20;            // 面板X坐标
input int    Dashboard_Y = 50;            // 面板Y坐标
input int    Font_Size = 9;               // 字体大小

//+------------------------------------------------------------------+
//| 全局变量                                                          |
//+------------------------------------------------------------------+
string EA_Name = "TrendBreakoutReversal";
int Magic_Breakout = 20241201;
int Magic_Reversal = 20241202;

// 固定时间框架设置
int EA_Timeframe = PERIOD_M3;  // 固定使用3分钟时间框架

// 统计变量
int Total_Trades = 0;
int Win_Trades = 0;
double Total_Profit = 0.0;
double Max_DD = 0.0;
double Peak_Balance = 0.0;

// 信号状态
bool Uptrend_Active = false;
bool Breakout_Signal = false;
bool Reversal_Signal = false;
double Current_EMA20 = 0.0;
double Current_EMA50 = 0.0;
double Current_RSI = 0.0;
double Current_ATR = 0.0;

// 最后处理的K线时间 (基于3分钟时间框架)
datetime Last_Bar_Time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("=== ", EA_Name, " EA 初始化 ===");
   Print("固定时间框架：3分钟 (M3)");

   // 检查交易品种
   if(Symbol() != "XAUUSD" && Symbol() != "GOLD")
   {
      Alert("警告：此EA专为XAUUSD设计，当前品种：", Symbol());
   }

   // 初始化统计数据
   Peak_Balance = AccountBalance();

   // 初始化3分钟时间框架的最后K线时间
   Last_Bar_Time = iTime(Symbol(), EA_Timeframe, 0);

   // 创建仪表板
   if(Show_Dashboard)
   {
      CreateDashboard();
   }

   Print("EA初始化完成 - 使用", EA_Timeframe, "分钟时间框架");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 清理图表对象
   ObjectsDeleteAll(0, "TBR_");
   Print("=== ", EA_Name, " EA 已停止 ===");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查是否有新的3分钟K线
   datetime current_bar_time = iTime(Symbol(), EA_Timeframe, 0);
   if(current_bar_time == Last_Bar_Time)
      return;
   Last_Bar_Time = current_bar_time;

   // 基本检查
   if(!IsTradeAllowed() || !IsTimeToTrade())
      return;

   // 检查点差
   double spread = MarketInfo(Symbol(), MODE_SPREAD);
   if(spread > Max_Spread)
      return;

   // 计算指标 (基于3分钟时间框架)
   CalculateIndicators();

   // 更新统计数据
   UpdateStatistics();

   // 检查交易信号
   CheckTradingSignals();

   // 管理现有订单
   ManageOrders();

   // 更新仪表板
   if(Show_Dashboard)
   {
      UpdateDashboard();
   }
}

//+------------------------------------------------------------------+
//| 计算指标 (固定使用3分钟时间框架)                                    |
//+------------------------------------------------------------------+
void CalculateIndicators()
{
   // 所有指标计算都基于3分钟时间框架
   Current_EMA20 = iMA(Symbol(), EA_Timeframe, EMA20_Period, 0, MODE_EMA, PRICE_CLOSE, 1);
   Current_EMA50 = iMA(Symbol(), EA_Timeframe, EMA50_Period, 0, MODE_EMA, PRICE_CLOSE, 1);
   Current_RSI = iRSI(Symbol(), EA_Timeframe, RSI_Period, PRICE_CLOSE, 1);
   Current_ATR = iATR(Symbol(), EA_Timeframe, ATR_Period, 1);

   // 趋势判断 (基于3分钟EMA斜率)
   double ema20_slope = Current_EMA20 - iMA(Symbol(), EA_Timeframe, EMA20_Period, 0, MODE_EMA, PRICE_CLOSE, 2);
   double ema50_slope = Current_EMA50 - iMA(Symbol(), EA_Timeframe, EMA50_Period, 0, MODE_EMA, PRICE_CLOSE, 2);

   Uptrend_Active = (Current_EMA20 > Current_EMA50) && (ema20_slope > 0) && (ema50_slope > 0);
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                      |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
   Breakout_Signal = false;
   Reversal_Signal = false;
   
   // 突破信号检查 (基于3分钟时间框架)
   if(Uptrend_Active)
   {
      double highest_high = iHigh(Symbol(), EA_Timeframe, iHighest(Symbol(), EA_Timeframe, MODE_HIGH, Breakout_Lookback, 2));
      double vol_ma = 0;

      // 计算3分钟成交量均线
      for(int i = 1; i <= Volume_MA_Period; i++)
      {
         vol_ma += iVolume(Symbol(), EA_Timeframe, i);
      }
      vol_ma = vol_ma / Volume_MA_Period;

      double current_close = iClose(Symbol(), EA_Timeframe, 1);
      double current_volume = iVolume(Symbol(), EA_Timeframe, 1);

      if(current_close > highest_high && current_volume > vol_ma * Volume_Factor)
      {
         Breakout_Signal = true;
      }
   }
   
   // 反转信号检查 (基于3分钟时间框架)
   if(!Uptrend_Active)
   {
      bool oversold = Current_RSI < RSI_Oversold;
      bool hammer = IsHammerCandle(1);
      bool rsi_divergence = CheckRSIDivergence();

      double current_close = iClose(Symbol(), EA_Timeframe, 1);
      bool price_near_ema50 = MathAbs(current_close - Current_EMA50) < Current_ATR * ATR_Support_Factor;

      if(oversold && ((hammer && rsi_divergence) || (hammer && price_near_ema50) || (rsi_divergence && price_near_ema50)))
      {
         Reversal_Signal = true;
      }
   }
   
   // 执行交易
   if(Breakout_Signal && GetOpenOrders(Magic_Breakout) == 0)
   {
      ExecuteBreakoutTrade();
   }

   if(Reversal_Signal && GetOpenOrders(Magic_Reversal) == 0)
   {
      ExecuteReversalTrade();
   }
}

//+------------------------------------------------------------------+
//| 执行突破交易                                                      |
//+------------------------------------------------------------------+
void ExecuteBreakoutTrade()
{
   // 基于3分钟时间框架计算止损
   double stop_loss = iLow(Symbol(), EA_Timeframe, iLowest(Symbol(), EA_Timeframe, MODE_LOW, 5, 2));
   double entry_price = Ask;
   double stop_distance = entry_price - stop_loss;

   if(stop_distance <= 0) return;

   double lot_size = CalculatePositionSize(stop_distance);
   double take_profit = Use_Fixed_TP ? entry_price + stop_distance * Profit_Ratio : 0;

   int ticket = OrderSend(Symbol(), OP_BUY, lot_size, entry_price, 3, stop_loss, take_profit,
                         "Breakout Long M3", Magic_Breakout, 0, clrGreen);

   if(ticket > 0)
   {
      Print("突破做多订单已开启(M3) - Ticket: ", ticket, " Lots: ", lot_size);
   }
   else
   {
      Print("突破做多订单失败 - Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| 执行反转交易                                                      |
//+------------------------------------------------------------------+
void ExecuteReversalTrade()
{
   // 基于3分钟时间框架计算止损
   double current_low = iLow(Symbol(), EA_Timeframe, 1);
   double stop_loss = current_low - Current_ATR * 0.5;
   double entry_price = Ask;
   double stop_distance = entry_price - stop_loss;

   if(stop_distance <= 0) return;

   double lot_size = CalculatePositionSize(stop_distance);
   double take_profit = Use_Fixed_TP ? entry_price + stop_distance * Profit_Ratio : 0;

   int ticket = OrderSend(Symbol(), OP_BUY, lot_size, entry_price, 3, stop_loss, take_profit,
                         "Reversal Long M3", Magic_Reversal, 0, clrLime);

   if(ticket > 0)
   {
      Print("反转做多订单已开启(M3) - Ticket: ", ticket, " Lots: ", lot_size);
   }
   else
   {
      Print("反转做多订单失败 - Error: ", GetLastError());
   }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
double CalculatePositionSize(double stop_distance)
{
   double risk_amount = AccountBalance() * Risk_Percent / 100.0;
   double tick_value = MarketInfo(Symbol(), MODE_TICKVALUE);
   double tick_size = MarketInfo(Symbol(), MODE_TICKSIZE);

   double stop_distance_money = stop_distance / tick_size * tick_value;
   double lot_size = risk_amount / stop_distance_money;

   // 标准化手数
   double min_lot = MarketInfo(Symbol(), MODE_MINLOT);
   double max_lot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lot_step = MarketInfo(Symbol(), MODE_LOTSTEP);

   lot_size = MathMax(min_lot, MathMin(max_lot, NormalizeDouble(lot_size / lot_step, 0) * lot_step));

   return lot_size;
}

//+------------------------------------------------------------------+
//| 检查锤子线                                                        |
//+------------------------------------------------------------------+
bool IsHammerCandle(int shift)
{
   // 基于3分钟时间框架检查锤子线
   double open_price = iOpen(Symbol(), EA_Timeframe, shift);
   double close_price = iClose(Symbol(), EA_Timeframe, shift);
   double high_price = iHigh(Symbol(), EA_Timeframe, shift);
   double low_price = iLow(Symbol(), EA_Timeframe, shift);

   double body_size = MathAbs(close_price - open_price);
   double total_size = high_price - low_price;
   double lower_shadow = MathMin(close_price, open_price) - low_price;
   double upper_shadow = high_price - MathMax(close_price, open_price);

   if(total_size == 0) return false;

   return (lower_shadow > 2 * body_size) && (upper_shadow < body_size) && (body_size > 0);
}

//+------------------------------------------------------------------+
//| 检查RSI底背离                                                     |
//+------------------------------------------------------------------+
bool CheckRSIDivergence()
{
   // 基于3分钟时间框架检查RSI底背离
   double current_low = iLow(Symbol(), EA_Timeframe, 1);
   double prev_low = iLow(Symbol(), EA_Timeframe, iLowest(Symbol(), EA_Timeframe, MODE_LOW, 5, 2));

   double current_rsi = Current_RSI;
   int lowest_bar = iLowest(Symbol(), EA_Timeframe, MODE_LOW, 5, 2);
   double prev_rsi = iRSI(Symbol(), EA_Timeframe, RSI_Period, PRICE_CLOSE, lowest_bar + 1);

   return (current_low < prev_low) && (current_rsi > prev_rsi);
}

//+------------------------------------------------------------------+
//| 获取指定魔术号的开仓订单数量                                        |
//+------------------------------------------------------------------+
int GetOpenOrders(int magic)
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magic)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| 管理现有订单                                                      |
//+------------------------------------------------------------------+
void ManageOrders()
{
   if(!Use_Trailing_Stop) return;

   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && (OrderMagicNumber() == Magic_Breakout || OrderMagicNumber() == Magic_Reversal))
         {
            if(OrderType() == OP_BUY)
            {
               ManageTrailingStop(OrderTicket());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 管理追踪止损                                                      |
//+------------------------------------------------------------------+
void ManageTrailingStop(int ticket)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET)) return;

   double current_price = Bid;
   double entry_price = OrderOpenPrice();
   double current_sl = OrderStopLoss();

   // 检查是否达到追踪触发条件
   double profit_points = (current_price - entry_price) / Point;

   if(profit_points >= Trail_Trigger_Points)
   {
      double new_sl = current_price - Trail_Stop_Points * Point;

      // 确保新止损比当前止损更有利
      if(new_sl > current_sl + Trail_Step_Points * Point)
      {
         bool result = OrderModify(ticket, entry_price, new_sl, OrderTakeProfit(), 0, clrBlue);
         if(result)
         {
            Print("追踪止损已更新 - Ticket: ", ticket, " New SL: ", new_sl);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 时间过滤检查                                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
   if(!Use_Time_Filter) return true;

   int current_hour = Hour();

   if(Start_Hour <= End_Hour)
   {
      return (current_hour >= Start_Hour && current_hour < End_Hour);
   }
   else
   {
      return (current_hour >= Start_Hour || current_hour < End_Hour);
   }
}

//+------------------------------------------------------------------+
//| 更新统计数据                                                      |
//+------------------------------------------------------------------+
void UpdateStatistics()
{
   double current_balance = AccountBalance();

   // 更新峰值余额
   if(current_balance > Peak_Balance)
   {
      Peak_Balance = current_balance;
   }

   // 计算最大回撤
   double current_dd = (Peak_Balance - current_balance) / Peak_Balance * 100;
   if(current_dd > Max_DD)
   {
      Max_DD = current_dd;
   }

   // 统计交易结果
   Total_Trades = 0;
   Win_Trades = 0;
   Total_Profit = 0;

   for(int i = 0; i < OrdersHistoryTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY))
      {
         if(OrderSymbol() == Symbol() && (OrderMagicNumber() == Magic_Breakout || OrderMagicNumber() == Magic_Reversal))
         {
            Total_Trades++;
            Total_Profit += OrderProfit() + OrderSwap() + OrderCommission();
            if(OrderProfit() > 0) Win_Trades++;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 创建仪表板                                                        |
//+------------------------------------------------------------------+
void CreateDashboard()
{
   string prefix = "TBR_";

   // 背景面板
   ObjectCreate(prefix + "Background", OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSet(prefix + "Background", OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSet(prefix + "Background", OBJPROP_XDISTANCE, Dashboard_X);
   ObjectSet(prefix + "Background", OBJPROP_YDISTANCE, Dashboard_Y);
   ObjectSet(prefix + "Background", OBJPROP_XSIZE, 300);
   ObjectSet(prefix + "Background", OBJPROP_YSIZE, 250);
   ObjectSet(prefix + "Background", OBJPROP_BGCOLOR, C'40,40,40');
   ObjectSet(prefix + "Background", OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSet(prefix + "Background", OBJPROP_COLOR, clrWhite);

   // 标题
   CreateLabel(prefix + "Title", Dashboard_X + 10, Dashboard_Y + 10, "趋势突破反转策略 (M3)", clrYellow, Font_Size + 2);

   // 时间框架显示
   CreateLabel(prefix + "Timeframe", Dashboard_X + 10, Dashboard_Y + 30, "时间框架: 3分钟 (固定)", clrCyan, Font_Size);

   // 指标状态标签
   CreateLabel(prefix + "EMA_Label", Dashboard_X + 10, Dashboard_Y + 50, "EMA状态:", clrWhite, Font_Size);
   CreateLabel(prefix + "RSI_Label", Dashboard_X + 10, Dashboard_Y + 70, "RSI:", clrWhite, Font_Size);
   CreateLabel(prefix + "ATR_Label", Dashboard_X + 10, Dashboard_Y + 90, "ATR:", clrWhite, Font_Size);
   CreateLabel(prefix + "Trend_Label", Dashboard_X + 10, Dashboard_Y + 110, "趋势:", clrWhite, Font_Size);

   // 信号状态标签
   CreateLabel(prefix + "Breakout_Label", Dashboard_X + 10, Dashboard_Y + 135, "突破信号:", clrWhite, Font_Size);
   CreateLabel(prefix + "Reversal_Label", Dashboard_X + 10, Dashboard_Y + 155, "反转信号:", clrWhite, Font_Size);

   // 统计标签
   CreateLabel(prefix + "Trades_Label", Dashboard_X + 10, Dashboard_Y + 180, "总交易:", clrWhite, Font_Size);
   CreateLabel(prefix + "WinRate_Label", Dashboard_X + 10, Dashboard_Y + 200, "胜率:", clrWhite, Font_Size);
   CreateLabel(prefix + "Profit_Label", Dashboard_X + 10, Dashboard_Y + 220, "总盈利:", clrWhite, Font_Size);
   CreateLabel(prefix + "DD_Label", Dashboard_X + 10, Dashboard_Y + 240, "最大回撤:", clrWhite, Font_Size);
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                      |
//+------------------------------------------------------------------+
void CreateLabel(string name, int x, int y, string text, color clr, int size)
{
   ObjectCreate(name, OBJ_LABEL, 0, 0, 0);
   ObjectSet(name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
   ObjectSet(name, OBJPROP_XDISTANCE, x);
   ObjectSet(name, OBJPROP_YDISTANCE, y);
   ObjectSetText(name, text, size, "Arial", clr);
}

//+------------------------------------------------------------------+
//| 更新仪表板                                                        |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
   string prefix = "TBR_";

   // 更新指标值
   ObjectSetText(prefix + "EMA_Label", "EMA状态: EMA20=" + DoubleToStr(Current_EMA20, Digits) +
                " EMA50=" + DoubleToStr(Current_EMA50, Digits), Font_Size, "Arial", clrWhite);

   ObjectSetText(prefix + "RSI_Label", "RSI: " + DoubleToStr(Current_RSI, 2), Font_Size, "Arial",
                Current_RSI < RSI_Oversold ? clrRed : clrWhite);

   ObjectSetText(prefix + "ATR_Label", "ATR: " + DoubleToStr(Current_ATR, Digits), Font_Size, "Arial", clrWhite);

   ObjectSetText(prefix + "Trend_Label", "趋势: " + (Uptrend_Active ? "上升" : "下降"), Font_Size, "Arial",
                Uptrend_Active ? clrLime : clrRed);

   // 更新信号状态
   ObjectSetText(prefix + "Breakout_Label", "突破信号: " + (Breakout_Signal ? "是" : "否"), Font_Size, "Arial",
                Breakout_Signal ? clrLime : clrGray);

   ObjectSetText(prefix + "Reversal_Label", "反转信号: " + (Reversal_Signal ? "是" : "否"), Font_Size, "Arial",
                Reversal_Signal ? clrLime : clrGray);

   // 更新统计数据
   ObjectSetText(prefix + "Trades_Label", "总交易: " + IntegerToString(Total_Trades), Font_Size, "Arial", clrWhite);

   double win_rate = Total_Trades > 0 ? (double)Win_Trades / Total_Trades * 100 : 0;
   ObjectSetText(prefix + "WinRate_Label", "胜率: " + DoubleToStr(win_rate, 1) + "%", Font_Size, "Arial",
                win_rate >= 50 ? clrLime : clrRed);

   ObjectSetText(prefix + "Profit_Label", "总盈利: $" + DoubleToStr(Total_Profit, 2), Font_Size, "Arial",
                Total_Profit >= 0 ? clrLime : clrRed);

   ObjectSetText(prefix + "DD_Label", "最大回撤: " + DoubleToStr(Max_DD, 2) + "%", Font_Size, "Arial",
                Max_DD < 10 ? clrLime : (Max_DD < 20 ? clrYellow : clrRed));
}
